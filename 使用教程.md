***
## 一.PVE安装和配置
软件包安装

~~~
apt update -y
apt install wget curl sudo git screen nano iptables-persistent iptables redis-server lsb-release unzip -y
~~~
### 1.安装PVE
这里只提供arm64,Debian12环境下安装脚本：
~~~
bash <(curl -s https://git.fsytool.top/xkatld/linuxtools/raw/branch/main/linuxtools.sh)
~~~
使用log:
~~~
root@localhost:~# bash <(curl -s https://git.fsytool.top/xkatld/linuxtools/raw/branch/main/linuxtools.sh)
[*] 正在检查核心依赖: curl mktemp sort...
=========================================
        Linux 工具箱 (作者: xkatld)        
=========================================
  1 ) LXD安装与镜像管理
  2 ) 虚拟内存综合管理
  3 ) linuxmirrors综合脚本
  4 ) SSH综合管理
  5 ) 系统优化综合脚本
  6 ) PVE安装与镜像管理
  ---------------------------------------
  0) 退出脚本
=========================================
请输入您的选择: 6
[*] 准备执行: PVE安装与镜像管理
[*] 正在从 https://raw.githubusercontent.com/xkatld/LinuxTools/refs/heads/main/shell/install-pve.sh 下载脚本...
[+] 脚本下载成功。
[-] 警告: 您即将从网络执行一个脚本，请确认您信任来源: https://raw.githubusercontent.com/xkatld/LinuxTools/refs/heads/main/shell/install-pve.sh
是否继续执行? (y/N): y
[*] 开始执行子脚本...
-------------------- 开始执行子脚本 --------------------

欢迎使用 Proxmox VE 通用安装脚本 (AMD64/ARM64)

>>> [步骤] 检查系统环境和依赖
[INFO] 检测到系统架构: arm64
[INFO] 所有依赖项均已满足。

>>> [步骤] 验证 Debian 版本
[INFO] 检测到 Debian 12 (Bookworm)，将准备安装 PVE 8

>>> [步骤] 根据架构 (arm64) 配置软件源
[INFO] 为 ARM64 架构选择第三方镜像源。
请选择一个地理位置较近的镜像源以获得更快的速度：
  1) 主源 (韩国)
  2) 中国 (Lierfang)
  3) 中国香港
  4) 德国
请输入选项数字 (1-4): 1
[INFO] 软件源地址已设置为: https://mirrors.apqa.cn/proxmox/debian/pve
[INFO] GPG密钥地址已设置为: https://mirrors.apqa.cn/proxmox/debian/pveport.gpg

>>> [步骤] 配置主机名和 /etc/hosts 文件
请输入主机名 (例如: pve): us1arm
请输入域名 (例如: local, home): com
请输入服务器的静态 IP 地址 (例如: ************): ************
[INFO] 配置预览：
  - 完整主机名 (FQDN): us1arm.com
  - IP 地址: ************
即将修改主机名并覆盖 /etc/hosts 文件，是否继续? (y/N): y
[INFO] 主机名已设置为: us1arm.com
[INFO] /etc/hosts 文件已成功更新。

====================== 最终安装确认 ======================
[INFO] 系统环境检查完成，配置如下：
  - 系统架构:        arm64
  - Debian 版本:     bookworm (PVE 8)
  - 主机名 (FQDN):   us1arm.com
  - 服务器 IP:       ************
  - 使用软件源:      https://mirrors.apqa.cn/proxmox/debian/pve
============================================================
即将开始不可逆的安装过程，是否继续? (y/N): y

>>> [步骤] 备份当前 APT 源配置
[INFO] 备份目录已创建: /root/pve_install_backup_20250708_095515
[INFO] 所有 .list 文件已备份。

>>> [步骤] 开始安装 Proxmox VE
[INFO] 正在下载 Proxmox GPG 密钥...
[INFO] GPG 密钥安装成功。
[INFO] 正在配置 Proxmox VE 的 APT 源...
[INFO] 正在更新软件包列表 (apt-get update)...
Hit:1 http://deb.debian.org/debian bookworm InRelease
...
Fetched 1,081 kB in 1s (1,015 kB/s) 
Reading package lists... Done
[INFO] 正在安装 Proxmox VE 核心包... 这可能需要一些时间。
Reading package lists... Done
Building dependency tree... Done
Reading state information... Done
The following additional packages will be installed:
  adwaita-icon-theme alsa-topology-conf alsa-ucm-conf at-spi2-common at-spi2-core attr binutils binutils-aarch64-linux-gnu binutils-common bridge-utils
...
Created symlink /etc/systemd/system/multi-user.target.wants/pve-guests.service → /lib/systemd/system/pve-guests.service.
  Configuration node devices/global_filter not found
Backing up lvm.conf before setting pve-manager specific settings..
'/etc/lvm/lvm.conf' -> '/etc/lvm/lvm.conf.bak'
Setting 'global_filter' in /etc/lvm/lvm.conf to prevent zvols and rbds from being scanned:
 => global_filter=["r|/dev/zd.*|","r|/dev/rbd.*|"]
  LVM configuration valid.
Setting up proxmox-ve (8.3.0) ...
Processing triggers for proxmox-backup-file-restore (3.3.2-2) ...
Updating file-restore initramfs...
12831 blocks
Processing triggers for libgdk-pixbuf-2.0-0:arm64 (2.42.10+dfsg-1+deb12u2) ...
Processing triggers for libc-bin (2.36-9+deb12u10) ...
[INFO] Proxmox VE 核心组件安装成功！

============================================================
[INFO]     Proxmox VE 8 安装成功!    
============================================================

[INFO] 请通过以下地址访问 Proxmox VE Web 管理界面:
  URL:      https://**********:8006/
  用户名:   root
  密码:     (您的系统 root 密码)

[WARN] 为了加载新的 Proxmox 内核，系统需要重启。
是否立即重启系统? (y/N): y
[INFO] 系统将在 5 秒后重启...

连接断开
~~~

### 2.网卡配置
NAT示例
~~~
source /etc/network/interfaces.d/*

auto lo
iface lo inet loopback

auto enp0s3
iface enp0s3 inet dhcp

auto vmbr0
iface vmbr0 inet static
    address ********/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    post-up iptables -t nat -A POSTROUTING -s '********/24' -o enp0s3 -j MASQUERADE
~~~

***
## 二.服务端配置

软件包安装

~~~
apt update -y
apt install wget curl sudo git screen nano iptables-persistent iptables redis-server lsb-release unzip -y
~~~

注:根据架构下载二进制压缩包

![image](https://github.com/user-attachments/assets/c84735e2-79d1-4681-9ade-f81e589455ee)

### 1.下载二进制文件

下面以arm架构为例:

~~~
mkdir server
cd server
wget https://github.com/xkatld/zjmf-pve-lxc-server/releases/download/v1.0.0-beta.1/linux_arm64.zip
unzip linux_arm64.zip
chmod 777 app celery_worker
~~~

### 2.修改app.ini配置文件
~~~
[server]
HTTP_PORT = 8080
TOKEN = 00269FA0F9875BEF358BE7C4484B89A5 自定义魔方连接使用
LOG_LEVEL = INFO

[pve]
API_HOST = 127.0.0.1
API_USER = root@pam
API_PASSWORD = 00269FA0F9875BEF358BE7C4484B89A5 root密码
NODE = us1arm PVE节点名称
STORAGE = local PVE节点储存名称
BRIDGE = vmbr0 lxc使用的ipv4网卡
BRIDGE_V6 = vmbr0 lxc使用的ipv6网卡
DEFAULT_TEMPLATE = local:vztmpl/debian-12-arm64_2025-06-18.tar.gz 默认镜像
MAIN_INTERFACE = enp0s6 外网网卡
NAT_LISTEN_IP = ********** 外网IP{注意是系统内外网网卡提供的IP}

[celery]
BROKER_URL = redis://127.0.0.1:6379/0
RESULT_BACKEND = redis://127.0.0.1:6379/1
~~~


### 3.运行二进制后端

运行可以多种方式运行这里使用screen为例:

~~~
#运行后端
screen -S pveserver-py
./app
~~~
~~~
#运行异步任务
screen -S pveserver-rd
./celery_worker
~~~


***
## 三.魔方插件和产品的配置

### 1.上传插件
当前[pveserver](https://github.com/xkatld/zjmf-pve-lxc-server/tree/main/pveserver)目录,上传至魔方财务/public/plugins/servers/pveserver目录.

### 2.添加接口

![image](https://github.com/user-attachments/assets/57f12a7f-dca3-4994-8b2d-a700a24abf63)

![image](https://github.com/user-attachments/assets/1c6bf9f9-0686-49dd-b4d2-43453caf404d)

### 3.编辑产品

固定套餐：

![image](https://github.com/user-attachments/assets/59625091-8203-460e-a955-56d755c5b5bb)

弹性套餐：

![image](https://github.com/user-attachments/assets/8bef8f42-441e-45d7-ae24-14ee1aab5193)


| 配置选项名称 | 配置项类型 | 子项名称 |
| :--- | :--- | :--- |
| CPU\|CPU | cpu核心单选 | 1\|1核 |
| Memory\|内存 | 内存单选 | 256\|256M |
| net_limit\|带宽 | 带宽单选 | 10\|100Mbps |
| os\|操作系统 | 操作系统 | local:vztmpl/centos-9.tar.gz\|Centos^Centos9 amd64 |
| Disk Space\|硬盘 | 系统盘容量单选 | 1024\|1G |

## 四.有偿配置

由于文本描述始终有限,插件和服务端本身免费使用,占时不提供免费部署服务和咨询。有尝部署30-100,有意联系微信号：fsynetcom