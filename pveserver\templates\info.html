<style type="text/css" media="all">
.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem;
}
.border-left-primary {
    border-left: .25rem solid #4e73df!important;
}
.border-left-success {
    border-left: .25rem solid #1cc88a!important;
}
.border-left-info {
    border-left: .25rem solid #36b9cc!important;
}
.border-left-warning {
    border-left: .25rem solid #f6c23e!important;
}
.text-gray-300 {
    color: #dddfeb!important;
}
.text-gray-800 {
    color: #5a5c69!important;
}
.font-weight-bold {
    font-weight: 700!important;
}
.text-xs {
    font-size: .7rem;
}
.text-uppercase {
    text-transform: uppercase!important;
}
.h-100 {
    height: 100%!important;
}
.py-2, .py-3 {
    padding-top: .5rem!important;
    padding-bottom: .5rem!important;
}
.py-3 {
    padding-top: .75rem!important;
    padding-bottom: .75rem!important;
}
.shadow {
    box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
}
.progress-sm {
    height: .5rem;
}
.card-body ul {
    padding-left: 1.2rem;
    line-height: 1.8;
}
</style>

<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">处理器</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.UsedCPU}% ({$data.CPUCores} Core)</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-microchip fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">内存</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.UsedRam} MB / {$data.TotalRam} MB</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-memory fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">硬盘</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.UsedDiskGB} GB / {$data.TotalDiskGB} GB</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hdd fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">流量</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.UseBandwidth} GB / {$data.Bandwidth} GB</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">网络和提示</h6>
    </div>
    <div class="card-body">
        <ul>
            <li><b>公网IP地址 (NAT):</b> {$PublicIP}</li>
            <li><b>内网IPv4地址:</b> {$data.IP}</li>
            {if $data.IPv6_Main && $data.IPv6_Main != 'N/A'}
            <li><b>公网IPv6地址:</b> {$data.IPv6_Display}</li>
            {/if}
            {if $data.IPv6_Display && $data.IPv6_Display != 'N/A'}
            <li><b>内网IPv6地址:</b> {$data.IPv6_Main}</li>
            {/if}
            <br>
            - 所有端口转发规则都基于此公网IP。
            <br>
            - 您的初始SSH端口已自动映射，请在产品详情页查看。
            - 部分操作(如创建、重装)响应较慢，请耐心等待，不要离开页面。
        </ul>
    </div>
</div>